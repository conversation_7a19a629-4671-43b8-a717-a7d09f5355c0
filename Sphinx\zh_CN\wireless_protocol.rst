.. _wireless_protocol:

无线通信协议
============

本文描述Tag Tracking Controller无线连接通信协议。此协议兼容以前已量产手柄协议，在原来协议上进行扩充。

协议概述
--------

无线通信包括时间同步无线协议和BLE通信协议。

.. figure:: _static/espressif-logo.svg
   :align: center

   图1 系统组成框图

通信协议网络拓扑如图2所示，整体系统分为2个方式：

- BLE网络
- 2.4G无线同步方式

.. figure:: _static/espressif-logo.svg
   :align: center
   
   图2 通信网络拓扑图

连接握手
--------

蓝牙广播和扫描应答信息
~~~~~~~~~~~~~~~~~~~~~~

Controller蓝牙广播包含4个域信息：

- Flags
- 设备名称  
- 服务UUID
- 产商自定义数据域

广播数据定义：

.. list-table:: Controller广播产商信息定义
   :widths: 20 20 20 20 20
   :header-rows: 1

   * - Company Identifiers
     - 场景ID
     - Project name
     - Controller 类型
     - 
   * - 0xF0
     - 0x00
     - 1B
     - 1B
     - 1B

服务UUID定义：

.. list-table:: Controller广播16bit UUID定义
   :widths: 20 20 20 20 20
   :header-rows: 1

   * - Mark 跟踪ID
     - Id0
     - Id1
     - Id2
     - Id3
   * - 正常状态UUID
     - 0xF000
     - 0xF001
     - 0xF002
     - 0xF003

[后续内容按照相同方式转换...]