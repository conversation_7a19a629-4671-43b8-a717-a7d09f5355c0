import os
import sys

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from constants import TARGET_NAMES, CONTROLLER_LIST
# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = 'controller_docs'
copyright = '2025, ssp97'
author = 'ssp97'

# PDF输出配置
pdf_title = 'Controller文档'
pdf_file_prefix = 'controller_docs'
latex_template_dir = '_static'  # 指向包含LaTeX模板的目录

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'breathe',
    "sphinx.ext.todo",
    "extensions.sphinx_idf_theme",
    'sphinxcontrib.blockdiag',
    'sphinxcontrib.seqdiag',
    'sphinxcontrib.actdiag',
    'sphinxcontrib.nwdiag',
    'sphinxcontrib.rackdiag',
    'sphinxcontrib.packetdiag',
    'sphinxcontrib.cairosvgconverter',

    "extensions.html_redirects",
    "extensions.latex_builder"
]

TARGETS = list(TARGET_NAMES.keys())

def setup_version_js(file):
    import json

    data = {
        "DEFAULTS": { "has_targets": False, "supported_targets": []},
        "VERSIONS": [
            { "name": "html", "has_targets": True, "supported_targets": [] },
        ],
        "IDF_TARGETS": []
    }
    
    data["DEFAULTS"]["supported_targets"] = TARGETS
    data["VERSIONS"][0]["supported_targets"] = TARGETS
    for i in TARGET_NAMES:
        data["IDF_TARGETS"].append({"text":TARGET_NAMES[i], "value":i})

    j = json.dumps(data)
    j = "var DOCUMENTATION_VERSIONS = " + j

    with open(file, "w") as f:
        f.write(j)

def excel_gen():
    from openpyxl import Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Controller类型映射表"

    header = ["序号", 
              "Controller类型", 
              "Controller种类名称", 
              "蓝牙广播设备名称", 
              "类型识别CODE/USB PID", 
              "备注说明", 
              "配对"
              ]
    data = []
    count = 1

    for i in CONTROLLER_LIST:
        controllers = CONTROLLER_LIST[i]
        for j in controllers["code"]:
            pair = "√" if controllers["pair"] else "×"
            data.append({"序号":count, 
                         "Controller类型": controllers["type"], 
                         "Controller种类名称": controllers["name"],
                         "蓝牙广播设备名称": controllers["code"][j],
                         "类型识别CODE/USB PID":"0x{j:04X}",
                         "备注说明":controllers["info"],
                         "配对":pair
                         })
            count += 1
    ws.append(header)
    for row in data:
        ws.append([row["序号"], 
                   row["Controller类型"], 
                   row["Controller种类名称"],
                   row["蓝牙广播设备名称"],
                   row["类型识别CODE/USB PID"],
                   row["备注说明"],
                   row["配对"]
                   ])
    wb.save("Controller类型映射表.xlsx")


templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']
source_suffix = ['.rst', '.md']
source_parsers = {'.md': 'recommonmark.parser.CommonMarkParser',
                  }
conditional_include_dict = {}
pygments_style = 'sphinx'
html_theme = 'sphinx_idf_theme'
# html_context = {
#     'display_github': True,  # Add 'Edit on Github' link instead of 'View page source'
#     'github_version': 0, #get_github_rev(),
# }
versions_url = './_static/docs_version.js'
setup_version_js(versions_url)
excel_gen()
latest_branch_name = "master"

master_doc = 'index'

languages = ['zh_CN']
language = 'zh_CN'
idf_target = TARGETS#["Controller", "GUN-MP5"]
idf_targets = TARGETS#["Controller", "GUN-MP5"]

# -- Options for LaTeX output ------------------------------------------------
pdf_file_prefix = 'controller_docs'
pdf_title = '控制器文档'

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

# LaTeX configuration
latex_engine = 'xelatex'
latex_elements = {
    'papersize': 'a4paper',
    'pointsize': '10pt',
    'preamble': r'''
\usepackage{fontspec}
\setmainfont{Noto Sans CJK SC}
\setsansfont{Noto Sans CJK SC}
''',
}

html_static_path = ['_static']

def add_tag(app, config):
    pass

def setup_user(app, config):
    app.add_css_file('theme_overrides.css')
    if config.user_setup_callback:
        config.user_setup_callback(app, config)
    return None

def setup(app):
    # app.add_config_value("pdf_file", "", 'env')
    # app.add_config_value("idf_target_title_dict", "", 'env')
    # app.add_config_value("languages", 'zh_CN', 'env')
    # print(list(app.config))
    

    if 'idf_target' not in app.config:
        app.add_config_value('idf_target', None, 'env')
        app.add_config_value('idf_targets', None, 'env')
    app.add_config_value('config_dir', None, 'env')
    app.add_config_value('doxyfile_dir', None, 'env')
    app.add_config_value('project_path', None, 'env')

    app.add_config_value('conditional_include_dict', None, 'env')
    app.add_config_value('docs_to_build', None, 'env')
    app.add_config_value('user_setup_callback', None, 'env')
    app.add_config_value('doc_id', None, 'env')

    app.connect('config-inited',  setup_config_values)
    app.connect('config-inited', setup_set_tags)
    app.connect('config-inited',  setup_html_context)
    # app.connect('config-inited',  setup_diag_font)
    app.connect('config-inited',  setup_html)
    app.connect('config-inited',  setup_user)
    # app.connect('config-inited',  setup_version_js)


def setup_config_values(app, config):
    # Sets up global config values needed by other extensions
    app.add_config_value('idf_target_title_dict', TARGET_NAMES, 'env')

def setup_set_tags(app, config):
    targets = app.config.idf_target
    if not isinstance(targets, (list, tuple)):
        targets = [targets]
    
    for target in targets:
        print(f"Processing target: {target}")
        if target not in CONTROLLER_LIST:
            continue
            
        add_tags = CONTROLLER_LIST[target]["tags"]
        print(f"Tags for {target}: {add_tags}")
        
        if not isinstance(add_tags, (list, tuple)):
            add_tags = [add_tags]
        
        for tag in add_tags:
            if isinstance(tag, str) and tag:
                if not app.tags.has(tag):
                    app.tags.add(tag)
            elif isinstance(tag, (list, tuple)):
                for subtag in tag:
                    if isinstance(subtag, str) and subtag and not app.tags.has(subtag):
                        app.tags.add(subtag)
    
    print("All tags added:", list(app.tags))

def setup_html_context(app, config):
    # Setup path for 'edit on github'-link
    config.html_context['conf_py_path'] = '/docs/{}/'.format(app.config.language)

def setup_diag_font(app, config):
    # blockdiag and other tools require a font which supports their character set
    # the font file is stored on the download server to save repo size
    font_name = {
        'en': 'DejaVuSans.ttf',
        'zh_CN': 'NotoSansSC-Regular.otf',
    }[app.config.language]

    font_path = os.path.abspath(os.path.join(language, '_static', font_name))
    assert os.path.exists(font_path)

    app.config.blockdiag_fontpath = font_path
    app.config.seqdiag_fontpath = font_path
    app.config.actdiag_fontpath = font_path
    app.config.nwdiag_fontpath = font_path
    app.config.rackdiag_fontpath = font_path
    app.config.packetdiag_fontpath = font_path



def setup_html(app, config):
    # Add any paths that contain custom static files (such as style sheets) here,
    # relative to this directory. They are copied after the builtin static files,
    # so a file named "default.css" will overwrite the builtin "default.css".
    # esp_docs_static_path = os.path.join(config.config_dir, '_static')
    # app.config.html_static_path.append(esp_docs_static_path)

    # The name of an image file (relative to this directory) to place at the top
    # of the sidebar.
    # app.config.html_logo = os.path.join("_static", 'espressif-logo.svg')
    pass



