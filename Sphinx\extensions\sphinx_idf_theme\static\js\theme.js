!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=21)}([function(e,t,r){const n=r(6),{MAX_LENGTH:i,MAX_SAFE_INTEGER:s}=r(5),{re:o,t:a}=r(3),l=r(7),{compareIdentifiers:h}=r(11);class u{constructor(e,t){if(t=l(t),e instanceof u){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError("Invalid Version: "+e);if(e.length>i)throw new TypeError(`version is longer than ${i} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?o[a.LOOSE]:o[a.FULL]);if(!r)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>s||this.major<0)throw new TypeError("Invalid major version");if(this.minor>s||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>s||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<s)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof u)){if("string"==typeof e&&e===this.version)return 0;e=new u(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof u||(e=new u(e,this.options)),h(this.major,e.major)||h(this.minor,e.minor)||h(this.patch,e.patch)}comparePre(e){if(e instanceof u||(e=new u(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],i=e.prerelease[t];if(n("prerelease compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return-1;if(r!==i)return h(r,i)}while(++t)}compareBuild(e){e instanceof u||(e=new u(e,this.options));let t=0;do{const r=this.build[t],i=e.build[t];if(n("prerelease compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return-1;if(r!==i)return h(r,i)}while(++t)}inc(e,t){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t),this.inc("pre",t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t),this.inc("pre",t);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{let e=this.prerelease.length;for(;--e>=0;)"number"==typeof this.prerelease[e]&&(this.prerelease[e]++,e=-2);-1===e&&this.prerelease.push(0)}t&&(this.prerelease[0]===t?isNaN(this.prerelease[1])&&(this.prerelease=[t,0]):this.prerelease=[t,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this}}e.exports=u},function(e,t,r){const n=r(0);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},function(e,t,r){class n{constructor(e,t){if(t=s(t),e instanceof n)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new n(e.raw,t);if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.format(),this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e,this.set=e.split(/\s*\|\|\s*/).map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw new TypeError("Invalid SemVer Range: "+e);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter(e=>!m(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&E(e[0])){this.set=[e];break}}this.format()}format(){return this.range=this.set.map(e=>e.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(e){e=e.trim();const t=`parseRange:${Object.keys(this.options).join(",")}:${e}`,r=i.get(t);if(r)return r;const n=this.options.loose,s=n?h[u.HYPHENRANGELOOSE]:h[u.HYPHENRANGE];e=e.replace(s,S(this.options.includePrerelease)),a("hyphen replace",e),e=e.replace(h[u.COMPARATORTRIM],c),a("comparator trim",e,h[u.COMPARATORTRIM]),e=(e=(e=e.replace(h[u.TILDETRIM],p)).replace(h[u.CARETTRIM],f)).split(/\s+/).join(" ");const l=n?h[u.COMPARATORLOOSE]:h[u.COMPARATOR],E=e.split(" ").map(e=>d(e,this.options)).join(" ").split(/\s+/).map(e=>A(e,this.options)).filter(this.options.loose?e=>!!e.match(l):()=>!0).map(e=>new o(e,this.options)),v=(E.length,new Map);for(const e of E){if(m(e))return[e];v.set(e.value,e)}v.size>1&&v.has("")&&v.delete("");const g=[...v.values()];return i.set(t,g),g}intersects(e,t){if(!(e instanceof n))throw new TypeError("a Range is required");return this.set.some(r=>v(r,t)&&e.set.some(e=>v(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(y(this.set[t],e,this.options))return!0;return!1}}e.exports=n;const i=new(r(39))({max:1e3}),s=r(7),o=r(9),a=r(6),l=r(0),{re:h,t:u,comparatorTrimReplace:c,tildeTrimReplace:p,caretTrimReplace:f}=r(3),m=e=>"<0.0.0-0"===e.value,E=e=>""===e.value,v=(e,t)=>{let r=!0;const n=e.slice();let i=n.pop();for(;r&&n.length;)r=n.every(e=>i.intersects(e,t)),i=n.pop();return r},d=(e,t)=>(a("comp",e,t),e=N(e,t),a("caret",e),e=w(e,t),a("tildes",e),e=T(e,t),a("xrange",e),e=$(e,t),a("stars",e),e),g=e=>!e||"x"===e.toLowerCase()||"*"===e,w=(e,t)=>e.trim().split(/\s+/).map(e=>I(e,t)).join(" "),I=(e,t)=>{const r=t.loose?h[u.TILDELOOSE]:h[u.TILDE];return e.replace(r,(t,r,n,i,s)=>{let o;return a("tilde",e,t,r,n,i,s),g(r)?o="":g(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:g(i)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:s?(a("replaceTilde pr",s),o=`>=${r}.${n}.${i}-${s} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${i} <${r}.${+n+1}.0-0`,a("tilde return",o),o})},N=(e,t)=>e.trim().split(/\s+/).map(e=>R(e,t)).join(" "),R=(e,t)=>{a("caret",e,t);const r=t.loose?h[u.CARETLOOSE]:h[u.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,(t,r,i,s,o)=>{let l;return a("caret",e,t,r,i,s,o),g(r)?l="":g(i)?l=`>=${r}.0.0${n} <${+r+1}.0.0-0`:g(s)?l="0"===r?`>=${r}.${i}.0${n} <${r}.${+i+1}.0-0`:`>=${r}.${i}.0${n} <${+r+1}.0.0-0`:o?(a("replaceCaret pr",o),l="0"===r?"0"===i?`>=${r}.${i}.${s}-${o} <${r}.${i}.${+s+1}-0`:`>=${r}.${i}.${s}-${o} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${s}-${o} <${+r+1}.0.0-0`):(a("no pr"),l="0"===r?"0"===i?`>=${r}.${i}.${s}${n} <${r}.${i}.${+s+1}-0`:`>=${r}.${i}.${s}${n} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${s} <${+r+1}.0.0-0`),a("caret return",l),l})},T=(e,t)=>(a("replaceXRanges",e,t),e.split(/\s+/).map(e=>O(e,t)).join(" ")),O=(e,t)=>{e=e.trim();const r=t.loose?h[u.XRANGELOOSE]:h[u.XRANGE];return e.replace(r,(r,n,i,s,o,l)=>{a("xRange",e,r,n,i,s,o,l);const h=g(i),u=h||g(s),c=u||g(o),p=c;return"="===n&&p&&(n=""),l=t.includePrerelease?"-0":"",h?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&p?(u&&(s=0),o=0,">"===n?(n=">=",u?(i=+i+1,s=0,o=0):(s=+s+1,o=0)):"<="===n&&(n="<",u?i=+i+1:s=+s+1),"<"===n&&(l="-0"),r=`${n+i}.${s}.${o}${l}`):u?r=`>=${i}.0.0${l} <${+i+1}.0.0-0`:c&&(r=`>=${i}.${s}.0${l} <${i}.${+s+1}.0-0`),a("xRange return",r),r})},$=(e,t)=>(a("replaceStars",e,t),e.trim().replace(h[u.STAR],"")),A=(e,t)=>(a("replaceGTE0",e,t),e.trim().replace(h[t.includePrerelease?u.GTE0PRE:u.GTE0],"")),S=e=>(t,r,n,i,s,o,a,l,h,u,c,p,f)=>`${r=g(n)?"":g(i)?`>=${n}.0.0${e?"-0":""}`:g(s)?`>=${n}.${i}.0${e?"-0":""}`:o?">="+r:`>=${r}${e?"-0":""}`} ${l=g(h)?"":g(u)?`<${+h+1}.0.0-0`:g(c)?`<${h}.${+u+1}.0-0`:p?`<=${h}.${u}.${c}-${p}`:e?`<${h}.${u}.${+c+1}-0`:"<="+l}`.trim(),y=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(a(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},function(e,t,r){const{MAX_SAFE_COMPONENT_LENGTH:n}=r(5),i=r(6),s=(t=e.exports={}).re=[],o=t.src=[],a=t.t={};let l=0;const h=(e,t,r)=>{const n=l++;i(n,t),a[e]=n,o[n]=t,s[n]=new RegExp(t,r?"g":void 0)};h("NUMERICIDENTIFIER","0|[1-9]\\d*"),h("NUMERICIDENTIFIERLOOSE","[0-9]+"),h("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),h("MAINVERSION",`(${o[a.NUMERICIDENTIFIER]})\\.(${o[a.NUMERICIDENTIFIER]})\\.(${o[a.NUMERICIDENTIFIER]})`),h("MAINVERSIONLOOSE",`(${o[a.NUMERICIDENTIFIERLOOSE]})\\.(${o[a.NUMERICIDENTIFIERLOOSE]})\\.(${o[a.NUMERICIDENTIFIERLOOSE]})`),h("PRERELEASEIDENTIFIER",`(?:${o[a.NUMERICIDENTIFIER]}|${o[a.NONNUMERICIDENTIFIER]})`),h("PRERELEASEIDENTIFIERLOOSE",`(?:${o[a.NUMERICIDENTIFIERLOOSE]}|${o[a.NONNUMERICIDENTIFIER]})`),h("PRERELEASE",`(?:-(${o[a.PRERELEASEIDENTIFIER]}(?:\\.${o[a.PRERELEASEIDENTIFIER]})*))`),h("PRERELEASELOOSE",`(?:-?(${o[a.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${o[a.PRERELEASEIDENTIFIERLOOSE]})*))`),h("BUILDIDENTIFIER","[0-9A-Za-z-]+"),h("BUILD",`(?:\\+(${o[a.BUILDIDENTIFIER]}(?:\\.${o[a.BUILDIDENTIFIER]})*))`),h("FULLPLAIN",`v?${o[a.MAINVERSION]}${o[a.PRERELEASE]}?${o[a.BUILD]}?`),h("FULL",`^${o[a.FULLPLAIN]}$`),h("LOOSEPLAIN",`[v=\\s]*${o[a.MAINVERSIONLOOSE]}${o[a.PRERELEASELOOSE]}?${o[a.BUILD]}?`),h("LOOSE",`^${o[a.LOOSEPLAIN]}$`),h("GTLT","((?:<|>)?=?)"),h("XRANGEIDENTIFIERLOOSE",o[a.NUMERICIDENTIFIERLOOSE]+"|x|X|\\*"),h("XRANGEIDENTIFIER",o[a.NUMERICIDENTIFIER]+"|x|X|\\*"),h("XRANGEPLAIN",`[v=\\s]*(${o[a.XRANGEIDENTIFIER]})(?:\\.(${o[a.XRANGEIDENTIFIER]})(?:\\.(${o[a.XRANGEIDENTIFIER]})(?:${o[a.PRERELEASE]})?${o[a.BUILD]}?)?)?`),h("XRANGEPLAINLOOSE",`[v=\\s]*(${o[a.XRANGEIDENTIFIERLOOSE]})(?:\\.(${o[a.XRANGEIDENTIFIERLOOSE]})(?:\\.(${o[a.XRANGEIDENTIFIERLOOSE]})(?:${o[a.PRERELEASELOOSE]})?${o[a.BUILD]}?)?)?`),h("XRANGE",`^${o[a.GTLT]}\\s*${o[a.XRANGEPLAIN]}$`),h("XRANGELOOSE",`^${o[a.GTLT]}\\s*${o[a.XRANGEPLAINLOOSE]}$`),h("COERCE",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?(?:$|[^\\d])`),h("COERCERTL",o[a.COERCE],!0),h("LONETILDE","(?:~>?)"),h("TILDETRIM",`(\\s*)${o[a.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",h("TILDE",`^${o[a.LONETILDE]}${o[a.XRANGEPLAIN]}$`),h("TILDELOOSE",`^${o[a.LONETILDE]}${o[a.XRANGEPLAINLOOSE]}$`),h("LONECARET","(?:\\^)"),h("CARETTRIM",`(\\s*)${o[a.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",h("CARET",`^${o[a.LONECARET]}${o[a.XRANGEPLAIN]}$`),h("CARETLOOSE",`^${o[a.LONECARET]}${o[a.XRANGEPLAINLOOSE]}$`),h("COMPARATORLOOSE",`^${o[a.GTLT]}\\s*(${o[a.LOOSEPLAIN]})$|^$`),h("COMPARATOR",`^${o[a.GTLT]}\\s*(${o[a.FULLPLAIN]})$|^$`),h("COMPARATORTRIM",`(\\s*)${o[a.GTLT]}\\s*(${o[a.LOOSEPLAIN]}|${o[a.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",h("HYPHENRANGE",`^\\s*(${o[a.XRANGEPLAIN]})\\s+-\\s+(${o[a.XRANGEPLAIN]})\\s*$`),h("HYPHENRANGELOOSE",`^\\s*(${o[a.XRANGEPLAINLOOSE]})\\s+-\\s+(${o[a.XRANGEPLAINLOOSE]})\\s*$`),h("STAR","(<|>)?=?\\s*\\*"),h("GTE0","^\\s*>=\\s*0.0.0\\s*$"),h("GTE0PRE","^\\s*>=\\s*0.0.0-0\\s*$")},function(e,t,r){const{MAX_LENGTH:n}=r(5),{re:i,t:s}=r(3),o=r(0),a=r(7);e.exports=(e,t)=>{if(t=a(t),e instanceof o)return e;if("string"!=typeof e)return null;if(e.length>n)return null;if(!(t.loose?i[s.LOOSE]:i[s.FULL]).test(e))return null;try{return new o(e,t)}catch(e){return null}}},function(e,t){const r=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={SEMVER_SPEC_VERSION:"2.0.0",MAX_LENGTH:256,MAX_SAFE_INTEGER:r,MAX_SAFE_COMPONENT_LENGTH:16}},function(e,t,r){(function(t){const r="object"==typeof t&&t.env&&t.env.NODE_DEBUG&&/\bsemver\b/i.test(t.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=r}).call(this,r(25))},function(e,t){const r=["includePrerelease","loose","rtl"];e.exports=e=>e?"object"!=typeof e?{loose:!0}:r.filter(t=>e[t]).reduce((e,t)=>(e[t]=!0,e),{}):{}},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>n(e,t,r)>0},function(e,t,r){const n=Symbol("SemVer ANY");class i{static get ANY(){return n}constructor(e,t){if(t=s(t),e instanceof i){if(e.loose===!!t.loose)return e;e=e.value}h("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,h("comp",this)}parse(e){const t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw new TypeError("Invalid comparator: "+e);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new u(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(h("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof i))throw new TypeError("a Comparator is required");if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),""===this.operator)return""===this.value||new c(e.value,t).test(this.value);if(""===e.operator)return""===e.value||new c(this.value,t).test(e.semver);const r=!(">="!==this.operator&&">"!==this.operator||">="!==e.operator&&">"!==e.operator),n=!("<="!==this.operator&&"<"!==this.operator||"<="!==e.operator&&"<"!==e.operator),s=this.semver.version===e.semver.version,o=!(">="!==this.operator&&"<="!==this.operator||">="!==e.operator&&"<="!==e.operator),a=l(this.semver,"<",e.semver,t)&&(">="===this.operator||">"===this.operator)&&("<="===e.operator||"<"===e.operator),h=l(this.semver,">",e.semver,t)&&("<="===this.operator||"<"===this.operator)&&(">="===e.operator||">"===e.operator);return r||n||s&&o||a||h}}e.exports=i;const s=r(7),{re:o,t:a}=r(3),l=r(20),h=r(6),u=r(0),c=r(2)},function(e,t,r){const n=r(2);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},function(e,t){const r=/^[0-9]+$/,n=(e,t)=>{const n=r.test(e),i=r.test(t);return n&&i&&(e=+e,t=+t),e===t?0:n&&!i?-1:i&&!n?1:e<t?-1:1};e.exports={compareIdentifiers:n,rcompareIdentifiers:(e,t)=>n(t,e)}},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>0===n(e,t,r)},function(e,t,r){const n=r(0);e.exports=(e,t,r)=>{const i=new n(e,r),s=new n(t,r);return i.compare(s)||i.compareBuild(s)}},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>n(e,t,r)<0},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>n(e,t,r)>=0},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>n(e,t,r)<=0},function(e,t,r){const n=r(0),i=r(9),{ANY:s}=i,o=r(2),a=r(10),l=r(8),h=r(14),u=r(16),c=r(15);e.exports=(e,t,r,p)=>{let f,m,E,v,d;switch(e=new n(e,p),t=new o(t,p),r){case">":f=l,m=u,E=h,v=">",d=">=";break;case"<":f=h,m=c,E=l,v="<",d="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,p))return!1;for(let r=0;r<t.set.length;++r){const n=t.set[r];let o=null,a=null;if(n.forEach(e=>{e.semver===s&&(e=new i(">=0.0.0")),o=o||e,a=a||e,f(e.semver,o.semver,p)?o=e:E(e.semver,a.semver,p)&&(a=e)}),o.operator===v||o.operator===d)return!1;if((!a.operator||a.operator===v)&&m(e,a.semver))return!1;if(a.operator===d&&E(e,a.semver))return!1}return!0}},function(e,t){e.exports=jQuery},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>0!==n(e,t,r)},function(e,t,r){const n=r(12),i=r(19),s=r(8),o=r(15),a=r(14),l=r(16);e.exports=(e,t,r,h)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,h);case"!=":return i(e,r,h);case">":return s(e,r,h);case">=":return o(e,r,h);case"<":return a(e,r,h);case"<=":return l(e,r,h);default:throw new TypeError("Invalid operator: "+t)}}},function(e,t,r){r(22),r(23),e.exports=r(53)},function(e,t,r){(function(){var t="undefined"!=typeof window?window.jQuery:r(18);e.exports.ThemeNav={navBar:null,win:null,winScroll:!1,winResize:!1,linkScroll:!1,winPosition:0,winHeight:null,docHeight:null,isRunning:!1,enable:function(e){var r=this;void 0===e&&(e=!0),r.isRunning||(r.isRunning=!0,t((function(t){r.init(t),r.reset(),r.win.on("hashchange",r.reset),e&&r.win.on("scroll",(function(){r.linkScroll||r.winScroll||(r.winScroll=!0,requestAnimationFrame((function(){r.onScroll()})))})),r.win.on("resize",(function(){r.winResize||(r.winResize=!0,requestAnimationFrame((function(){r.onResize()})))})),r.onResize()})))},enableSticky:function(){this.enable(!0)},init:function(e){e(document);var t=this;this.navBar=e("div.wy-side-scroll:first"),this.win=e(window),e(document).on("click","[data-toggle='wy-nav-top']",(function(){e("[data-toggle='wy-nav-shift']").toggleClass("shift"),e("[data-toggle='rst-versions']").toggleClass("shift")})).on("click",".wy-menu-vertical .current ul li a",(function(){var r=e(this);e("[data-toggle='wy-nav-shift']").removeClass("shift"),e("[data-toggle='rst-versions']").toggleClass("shift"),t.toggleCurrent(r),t.hashChange()})).on("click","[data-toggle='rst-current-version']",(function(){e("[data-toggle='rst-versions']").toggleClass("shift-up")})),e("table.docutils:not(.field-list,.footnote,.citation)").wrap("<div class='wy-table-responsive'></div>"),e("table.docutils.footnote").wrap("<div class='wy-table-responsive footnote'></div>"),e("table.docutils.citation").wrap("<div class='wy-table-responsive citation'></div>"),e(".wy-menu-vertical ul").not(".simple").siblings("a").each((function(){var r=e(this);expand=e('<button class="toctree-expand" title="Open/close menu"></button>'),expand.on("click",(function(e){return t.toggleCurrent(r),e.stopPropagation(),!1})),r.prepend(expand)}))},reset:function(){var e=encodeURI(window.location.hash)||"#";try{var t=$(".wy-menu-vertical"),r=t.find('[href="'+e+'"]');if(0===r.length){var n=$('.document [id="'+e.substring(1)+'"]').closest("div.section");0===(r=t.find('[href="#'+n.attr("id")+'"]')).length&&(r=t.find('[href="#"]'))}if(r.length>0){$(".wy-menu-vertical .current").removeClass("current").attr("aria-expanded","false"),r.addClass("current").attr("aria-expanded","true"),r.closest("li.toctree-l1").parent().addClass("current").attr("aria-expanded","true");for(let e=1;e<=10;e++)r.closest("li.toctree-l"+e).addClass("current").attr("aria-expanded","true");r[0].scrollIntoView()}}catch(e){console.log("Error expanding nav for anchor",e)}},onScroll:function(){this.winScroll=!1;var e=this.win.scrollTop(),t=e+this.winHeight,r=this.navBar.scrollTop()+(e-this.winPosition);e<0||t>this.docHeight||(this.navBar.scrollTop(r),this.winPosition=e)},onResize:function(){this.winResize=!1,this.winHeight=this.win.height(),this.docHeight=$(document).height()},hashChange:function(){this.linkScroll=!0,this.win.one("hashchange",(function(){this.linkScroll=!1}))},toggleCurrent:function(e){var t=e.closest("li");t.siblings("li.current").removeClass("current").attr("aria-expanded","false"),t.siblings().find("li.current").removeClass("current").attr("aria-expanded","false");var r=t.find("> ul li");r.length&&(r.removeClass("current").attr("aria-expanded","false"),t.toggleClass("current").attr("aria-expanded",(function(e,t){return"true"==t?"false":"true"})))}},"undefined"!=typeof window&&(window.SphinxRtdTheme={Navigation:e.exports.ThemeNav,StickyNav:e.exports.ThemeNav}),function(){for(var e=0,t=["ms","moz","webkit","o"],r=0;r<t.length&&!window.requestAnimationFrame;++r)window.requestAnimationFrame=window[t[r]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[r]+"CancelAnimationFrame"]||window[t[r]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(t,r){var n=(new Date).getTime(),i=Math.max(0,16-(n-e)),s=window.setTimeout((function(){t(n+i)}),i);return e=n+i,s}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}()}).call(window)},function(e,t,r){},function(e,t,r){const n=r(3);e.exports={re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:r(5).SEMVER_SPEC_VERSION,SemVer:r(0),compareIdentifiers:r(11).compareIdentifiers,rcompareIdentifiers:r(11).rcompareIdentifiers,parse:r(4),valid:r(26),clean:r(27),inc:r(28),diff:r(29),major:r(30),minor:r(31),patch:r(32),prerelease:r(33),compare:r(1),rcompare:r(34),compareLoose:r(35),compareBuild:r(13),sort:r(36),rsort:r(37),gt:r(8),lt:r(14),eq:r(12),neq:r(19),gte:r(15),lte:r(16),cmp:r(20),coerce:r(38),Comparator:r(9),Range:r(2),satisfies:r(10),toComparators:r(42),maxSatisfying:r(43),minSatisfying:r(44),minVersion:r(45),validRange:r(46),outside:r(17),gtr:r(47),ltr:r(48),intersects:r(49),simplifyRange:r(50),subset:r(51)}},function(e,t){var r,n,i=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===s||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:s}catch(e){r=s}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(e){n=o}}();var l,h=[],u=!1,c=-1;function p(){u&&l&&(u=!1,l.length?h=l.concat(h):c=-1,h.length&&f())}function f(){if(!u){var e=a(p);u=!0;for(var t=h.length;t;){for(l=h,h=[];++c<t;)l&&l[c].run();c=-1,t=h.length}l=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function m(e,t){this.fun=e,this.array=t}function E(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];h.push(new m(e,t)),1!==h.length||u||a(f)},m.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=E,i.addListener=E,i.once=E,i.off=E,i.removeListener=E,i.removeAllListeners=E,i.emit=E,i.prependListener=E,i.prependOnceListener=E,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,r){const n=r(4);e.exports=(e,t)=>{const r=n(e,t);return r?r.version:null}},function(e,t,r){const n=r(4);e.exports=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},function(e,t,r){const n=r(0);e.exports=(e,t,r,i)=>{"string"==typeof r&&(i=r,r=void 0);try{return new n(e,r).inc(t,i).version}catch(e){return null}}},function(e,t,r){const n=r(4),i=r(12);e.exports=(e,t)=>{if(i(e,t))return null;{const r=n(e),i=n(t),s=r.prerelease.length||i.prerelease.length,o=s?"pre":"",a=s?"prerelease":"";for(const e in r)if(("major"===e||"minor"===e||"patch"===e)&&r[e]!==i[e])return o+e;return a}}},function(e,t,r){const n=r(0);e.exports=(e,t)=>new n(e,t).major},function(e,t,r){const n=r(0);e.exports=(e,t)=>new n(e,t).minor},function(e,t,r){const n=r(0);e.exports=(e,t)=>new n(e,t).patch},function(e,t,r){const n=r(4);e.exports=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},function(e,t,r){const n=r(1);e.exports=(e,t,r)=>n(t,e,r)},function(e,t,r){const n=r(1);e.exports=(e,t)=>n(e,t,!0)},function(e,t,r){const n=r(13);e.exports=(e,t)=>e.sort((e,r)=>n(e,r,t))},function(e,t,r){const n=r(13);e.exports=(e,t)=>e.sort((e,r)=>n(r,e,t))},function(e,t,r){const n=r(0),i=r(4),{re:s,t:o}=r(3);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let t;for(;(t=s[o.COERCERTL].exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&t.index+t[0].length===r.index+r[0].length||(r=t),s[o.COERCERTL].lastIndex=t.index+t[1].length+t[2].length;s[o.COERCERTL].lastIndex=-1}else r=e.match(s[o.COERCE]);return null===r?null:i(`${r[2]}.${r[3]||"0"}.${r[4]||"0"}`,t)}},function(e,t,r){"use strict";const n=r(40),i=Symbol("max"),s=Symbol("length"),o=Symbol("lengthCalculator"),a=Symbol("allowStale"),l=Symbol("maxAge"),h=Symbol("dispose"),u=Symbol("noDisposeOnSet"),c=Symbol("lruList"),p=Symbol("cache"),f=Symbol("updateAgeOnGet"),m=()=>1;const E=(e,t,r)=>{const n=e[p].get(t);if(n){const t=n.value;if(v(e,t)){if(g(e,n),!e[a])return}else r&&(e[f]&&(n.value.now=Date.now()),e[c].unshiftNode(n));return t.value}},v=(e,t)=>{if(!t||!t.maxAge&&!e[l])return!1;const r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[l]&&r>e[l]},d=e=>{if(e[s]>e[i])for(let t=e[c].tail;e[s]>e[i]&&null!==t;){const r=t.prev;g(e,t),t=r}},g=(e,t)=>{if(t){const r=t.value;e[h]&&e[h](r.key,r.value),e[s]-=r.length,e[p].delete(r.key),e[c].removeNode(t)}};class w{constructor(e,t,r,n,i){this.key=e,this.value=t,this.length=r,this.now=n,this.maxAge=i||0}}const I=(e,t,r,n)=>{let i=r.value;v(e,i)&&(g(e,r),e[a]||(i=void 0)),i&&t.call(n,i.value,i.key,e)};e.exports=class{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw new TypeError("max must be a non-negative number");this[i]=e.max||1/0;const t=e.length||m;if(this[o]="function"!=typeof t?m:t,this[a]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw new TypeError("maxAge must be a number");this[l]=e.maxAge||0,this[h]=e.dispose,this[u]=e.noDisposeOnSet||!1,this[f]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw new TypeError("max must be a non-negative number");this[i]=e||1/0,d(this)}get max(){return this[i]}set allowStale(e){this[a]=!!e}get allowStale(){return this[a]}set maxAge(e){if("number"!=typeof e)throw new TypeError("maxAge must be a non-negative number");this[l]=e,d(this)}get maxAge(){return this[l]}set lengthCalculator(e){"function"!=typeof e&&(e=m),e!==this[o]&&(this[o]=e,this[s]=0,this[c].forEach(e=>{e.length=this[o](e.value,e.key),this[s]+=e.length})),d(this)}get lengthCalculator(){return this[o]}get length(){return this[s]}get itemCount(){return this[c].length}rforEach(e,t){t=t||this;for(let r=this[c].tail;null!==r;){const n=r.prev;I(this,e,r,t),r=n}}forEach(e,t){t=t||this;for(let r=this[c].head;null!==r;){const n=r.next;I(this,e,r,t),r=n}}keys(){return this[c].toArray().map(e=>e.key)}values(){return this[c].toArray().map(e=>e.value)}reset(){this[h]&&this[c]&&this[c].length&&this[c].forEach(e=>this[h](e.key,e.value)),this[p]=new Map,this[c]=new n,this[s]=0}dump(){return this[c].map(e=>!v(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[c]}set(e,t,r){if((r=r||this[l])&&"number"!=typeof r)throw new TypeError("maxAge must be a number");const n=r?Date.now():0,a=this[o](t,e);if(this[p].has(e)){if(a>this[i])return g(this,this[p].get(e)),!1;const o=this[p].get(e).value;return this[h]&&(this[u]||this[h](e,o.value)),o.now=n,o.maxAge=r,o.value=t,this[s]+=a-o.length,o.length=a,this.get(e),d(this),!0}const f=new w(e,t,a,n,r);return f.length>this[i]?(this[h]&&this[h](e,t),!1):(this[s]+=f.length,this[c].unshift(f),this[p].set(e,this[c].head),d(this),!0)}has(e){if(!this[p].has(e))return!1;const t=this[p].get(e).value;return!v(this,t)}get(e){return E(this,e,!0)}peek(e){return E(this,e,!1)}pop(){const e=this[c].tail;return e?(g(this,e),e.value):null}del(e){g(this,this[p].get(e))}load(e){this.reset();const t=Date.now();for(let r=e.length-1;r>=0;r--){const n=e[r],i=n.e||0;if(0===i)this.set(n.k,n.v);else{const e=i-t;e>0&&this.set(n.k,n.v,e)}}}prune(){this[p].forEach((e,t)=>E(this,t,!1))}}},function(e,t,r){"use strict";function n(e){var t=this;if(t instanceof n||(t=new n),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach((function(e){t.push(e)}));else if(arguments.length>0)for(var r=0,i=arguments.length;r<i;r++)t.push(arguments[r]);return t}function i(e,t,r){var n=t===e.head?new a(r,null,t,e):new a(r,t,t.next,e);return null===n.next&&(e.tail=n),null===n.prev&&(e.head=n),e.length++,n}function s(e,t){e.tail=new a(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function o(e,t){e.head=new a(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function a(e,t,r,n){if(!(this instanceof a))return new a(e,t,r,n);this.list=n,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}e.exports=n,n.Node=a,n.create=n,n.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},n.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},n.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},n.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)s(this,arguments[e]);return this.length},n.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)o(this,arguments[e]);return this.length},n.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},n.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},n.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,n=0;null!==r;n++)e.call(t,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,n=this.length-1;null!==r;n--)e.call(t,r.value,n,this),r=r.prev},n.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},n.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},n.prototype.map=function(e,t){t=t||this;for(var r=new n,i=this.head;null!==i;)r.push(e.call(t,i.value,this)),i=i.next;return r},n.prototype.mapReverse=function(e,t){t=t||this;for(var r=new n,i=this.tail;null!==i;)r.push(e.call(t,i.value,this)),i=i.prev;return r},n.prototype.reduce=function(e,t){var r,n=this.head;if(arguments.length>1)r=t;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");n=this.head.next,r=this.head.value}for(var i=0;null!==n;i++)r=e(r,n.value,i),n=n.next;return r},n.prototype.reduceReverse=function(e,t){var r,n=this.tail;if(arguments.length>1)r=t;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");n=this.tail.prev,r=this.tail.value}for(var i=this.length-1;null!==n;i--)r=e(r,n.value,i),n=n.prev;return r},n.prototype.toArray=function(){for(var e=new Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},n.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},n.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,s=this.head;null!==s&&i<e;i++)s=s.next;for(;null!==s&&i<t;i++,s=s.next)r.push(s.value);return r},n.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,s=this.tail;null!==s&&i>t;i--)s=s.prev;for(;null!==s&&i>e;i--,s=s.prev)r.push(s.value);return r},n.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var n=0,s=this.head;null!==s&&n<e;n++)s=s.next;var o=[];for(n=0;s&&n<t;n++)o.push(s.value),s=this.removeNode(s);null===s&&(s=this.tail),s!==this.head&&s!==this.tail&&(s=s.prev);for(n=0;n<r.length;n++)s=i(this,s,r[n]);return o},n.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=t,this.tail=e,this};try{r(41)(n)}catch(e){}},function(e,t,r){"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},function(e,t,r){const n=r(2);e.exports=(e,t)=>new n(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},function(e,t,r){const n=r(0),i=r(2);e.exports=(e,t,r)=>{let s=null,o=null,a=null;try{a=new i(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(s&&-1!==o.compare(e)||(s=e,o=new n(s,r)))}),s}},function(e,t,r){const n=r(0),i=r(2);e.exports=(e,t,r)=>{let s=null,o=null,a=null;try{a=new i(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(s&&1!==o.compare(e)||(s=e,o=new n(s,r)))}),s}},function(e,t,r){const n=r(0),i=r(2),s=r(8);e.exports=(e,t)=>{e=new i(e,t);let r=new n("0.0.0");if(e.test(r))return r;if(r=new n("0.0.0-0"),e.test(r))return r;r=null;for(let t=0;t<e.set.length;++t){const i=e.set[t];let o=null;i.forEach(e=>{const t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":o&&!s(t,o)||(o=t);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+e.operator)}}),!o||r&&!s(r,o)||(r=o)}return r&&e.test(r)?r:null}},function(e,t,r){const n=r(2);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},function(e,t,r){const n=r(17);e.exports=(e,t,r)=>n(e,t,">",r)},function(e,t,r){const n=r(17);e.exports=(e,t,r)=>n(e,t,"<",r)},function(e,t,r){const n=r(2);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t))},function(e,t,r){const n=r(10),i=r(1);e.exports=(e,t,r)=>{const s=[];let o=null,a=null;const l=e.sort((e,t)=>i(e,t,r));for(const e of l){n(e,t,r)?(a=e,o||(o=e)):(a&&s.push([o,a]),a=null,o=null)}o&&s.push([o,null]);const h=[];for(const[e,t]of s)e===t?h.push(e):t||e!==l[0]?t?e===l[0]?h.push("<="+t):h.push(`${e} - ${t}`):h.push(">="+e):h.push("*");const u=h.join(" || "),c="string"==typeof t.raw?t.raw:String(t);return u.length<c.length?u:t}},function(e,t,r){const n=r(2),i=r(9),{ANY:s}=i,o=r(10),a=r(1),l=(e,t,r)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===s){if(1===t.length&&t[0].semver===s)return!0;e=r.includePrerelease?[new i(">=0.0.0-0")]:[new i(">=0.0.0")]}if(1===t.length&&t[0].semver===s){if(r.includePrerelease)return!0;t=[new i(">=0.0.0")]}const n=new Set;let l,c,p,f,m,E,v;for(const t of e)">"===t.operator||">="===t.operator?l=h(l,t,r):"<"===t.operator||"<="===t.operator?c=u(c,t,r):n.add(t.semver);if(n.size>1)return null;if(l&&c){if(p=a(l.semver,c.semver,r),p>0)return null;if(0===p&&(">="!==l.operator||"<="!==c.operator))return null}for(const e of n){if(l&&!o(e,String(l),r))return null;if(c&&!o(e,String(c),r))return null;for(const n of t)if(!o(e,String(n),r))return!1;return!0}let d=!(!c||r.includePrerelease||!c.semver.prerelease.length)&&c.semver,g=!(!l||r.includePrerelease||!l.semver.prerelease.length)&&l.semver;d&&1===d.prerelease.length&&"<"===c.operator&&0===d.prerelease[0]&&(d=!1);for(const e of t){if(v=v||">"===e.operator||">="===e.operator,E=E||"<"===e.operator||"<="===e.operator,l)if(g&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===g.major&&e.semver.minor===g.minor&&e.semver.patch===g.patch&&(g=!1),">"===e.operator||">="===e.operator){if(f=h(l,e,r),f===e&&f!==l)return!1}else if(">="===l.operator&&!o(l.semver,String(e),r))return!1;if(c)if(d&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===d.major&&e.semver.minor===d.minor&&e.semver.patch===d.patch&&(d=!1),"<"===e.operator||"<="===e.operator){if(m=u(c,e,r),m===e&&m!==c)return!1}else if("<="===c.operator&&!o(c.semver,String(e),r))return!1;if(!e.operator&&(c||l)&&0!==p)return!1}return!(l&&E&&!c&&0!==p)&&(!(c&&v&&!l&&0!==p)&&(!g&&!d))},h=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},u=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let i=!1;e:for(const n of e.set){for(const e of t.set){const t=l(n,e,r);if(i=i||null!==t,t)continue e}if(i)return!1}return!0}},,function(e,t,r){"use strict";r.r(t);const n={en:"You are reading the documentation for a release version that is end of life. ",zh_CN:"当前文档对应的版本支持期限已满，版本停止更新维护。"},i={en:"This documentation is not for the latest stable release version. ",zh_CN:"当前文档对应的版本不是最新稳定版。"},s={en:"There is a newer bugfix release of this version. ",zh_CN:"当前版本已发布新的 Bugfix。"},o={en:"You are reading the documentation for a pre-release version. ",zh_CN:"当前文档为预发布版本的配套文档。"},a={en:"The latest stable version is ",zh_CN:"最新稳定版本是 "},l={en:"The latest bugfix release is ",zh_CN:"最新 Bugfix 发布是"};function h(e){const t=document.createElement("div");t.className="admonition warning";const r=document.createElement("p");r.className="first admonition-title";const n=document.createTextNode("Note");r.append(n);let i=document.createElement("p");i.className="last",i.innerHTML=e,t.appendChild(r),t.appendChild(i);document.getElementsByClassName("document")[0].prepend(t)}var u="undefined"!=typeof window?window.jQuery:r(18);const c=r(24);function p(e,t){return"latest"==e?t+" (latest)":e.startsWith("release-")?e.replace("release-","release/"):e}function f(e){return"stable"==e.name||!/^v[0-9\.]+$/.test(e.name)}function m(){let e=DOCUMENTATION_VERSIONS.VERSIONS,t=null;for(let r=0;r<e.length;r++){let n=e[r];n.name.startsWith("v")&&!f(n)&&(!t||c.coerce(n.name)>c.coerce(t.name))&&(t=n)}return t}function E(e){const t=m();return t&&e.name===t.name}function v(e,t){return void 0===t||(void 0===e.supported_targets?"esp32"==e.name:e.supported_targets.includes(t))}"undefined"!=typeof window&&u((function(e){let t=DOCUMENTATION_OPTIONS.RELEASE,r=DOCUMENTATION_OPTIONS.LANGUAGE,u=DOCUMENTATION_OPTIONS.IDF_TARGET,d=DOCUMENTATION_OPTIONS.PAGENAME+".html",g=DOCUMENTATION_OPTIONS.LATEST_BRANCH_NAME;if("undefined"==typeof DOCUMENTATION_VERSIONS)return;let w=DOCUMENTATION_VERSIONS.DEFAULTS?DOCUMENTATION_VERSIONS.DEFAULTS:[],I=DOCUMENTATION_VERSIONS.VERSIONS?DOCUMENTATION_VERSIONS.VERSIONS:[],N=DOCUMENTATION_VERSIONS.IDF_TARGETS?DOCUMENTATION_VERSIONS.IDF_TARGETS:[],R=I[0];for(let e=0;e<I.length;e++){let r=I[e];for(let e in w)e in r||(r[e]=w[e]);r.name===t&&(R=r)}let T=DOCUMENTATION_OPTIONS.URL_ROOT+"../..";function O(e,t){let n=T+"/"+r+"/"+e;return t&&(n+="/"+(u||"esp32")),n+="/"+d,n}function $(){var t=e("#version-select option:selected");window.location.href=O(t.val(),t.data("has_target"))}function A(){var n=e("#target-select").val();window.location.href=function(e){let n=T+"/"+r,i=I.find(e=>e.name===t);return i?(v(i,e)?n+="/"+i.name:(i=I.find(t=>v(t,e)),n+="/"+(i?i.name:t)),i.has_targets&&(n+="/"+e)):n+="/"+t+"/"+e,n+="/"+d,n}(n)}DOCUMENTATION_OPTIONS.HAS_IDF_TARGETS&&(T+="/.."),N.length>0&&function(){var e=document.getElementById("target-select");e.hidden=!1,e.onchange=A;for(let r=0;r<N.length;r++){let n=N[r];var t=new Option(n.text,n.value);e.add(t)}e.value=u}(),I.length>0&&function(){var e=document.getElementById("version-select");e.hidden=!1,e.onchange=$;var r=!1;function n(n,i,s){var o=new Option(i,n);o.setAttribute("data-has_target",s),e.add(o),n==t&&(r=!0)}var i=m();for(let e=0;e<I.length;e++){let t=I[e];if(!t.old&&!t.pre_release&&"latest"!=t.name){if(i&&t.name==i.name)var s=`stable (${i.name})`;else s=p(t.name,g);v(t,u)&&n(t.name,s,t.has_targets)}}(o=new Option("Pre-Release Versions","")).disabled=!0,e.add(o);for(let e=0;e<I.length;e++){let t=I[e];"latest"==t.name&&n(t.name,p(t.name,g),t.has_targets)}for(let e=0;e<I.length;e++){let t=I[e];!t.old&&t.pre_release&&v(t,u)&&n(t.name,p(t.name,g),t.has_targets)}if(!r){var o=new Option(p(t,g),t,!0,!0);e.add(o)}e.value=t}(),I.length>0&&function(){if("latest"===R.name||E(R))return;const e=m();if(!e)return;const t=O(e.name,e.has_targets),u=function(e){const t=DOCUMENTATION_VERSIONS.VERSIONS,r=c.validRange("~"+c.coerce(e.name));for(let e=0;e<t.length;e++){const n=t[e];if(c.satisfies(c.coerce(n.name),r)&&!n.old)return n}}(R);let p="";u&&(p=O(u.name,u.has_targets)),R.end_of_life?function(e,t,r){const i='<a href="'+e+'">'+t+"</a>";h("<p> "+n[r]+a[r]+i+"</p>")}(t,e.name,r):R.old&&u?function(e,t,r){const n='<a href="'+e+'">'+t+"</a>";h("<p> "+s[r]+l[r]+n+"</p>")}(p,u.name,r):f(R)?function(e,t,r){const n='<a href="'+e+'">'+t+"</a>";h("<p> "+o[r]+a[r]+n+"</p>")}(t,e.name,r):E(R)||function(e,t,r){const n='<a href="'+e+'">'+t+"</a>";h("<p> "+i[r]+a[r]+n+"</p>")}(t,e.name,r)}()}))}]);