按键域对应关系
===============

.. only:: gun_92

    .. csv-table:: 按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "弹夹装入", ""
        "Hand-Left(Bit1)", "弹夹释放", ""
        "W-KEY(Bit2)", "上膛",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "Function按键", ""
        "Reserved-5bit", "霍尔入套检测", ""
        "Reserved-6bit", "握枪TP", ""
        "Reserved-7bit", "食指TP", ""


.. only:: gun_95

    .. csv-table:: 按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "弹夹装入", ""
        "Hand-Left(Bit1)", "弹夹释放", ""
        "W-KEY(Bit2)", "上膛",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "调速0(单发)", ""
        "Reserved-5bit", "调速1(连发)", ""
        "Reserved-6bit", "握枪TP", ""
        "Reserved-7bit", "食指TP", ""


.. only:: IR_CON

    .. csv-table:: 按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "B/Y 按键", ""
        "Hand-Left(Bit1)", "A/X 按键", ""
        "W-KEY(Bit2)", "摇杆按键",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "拇指休息区按键", ""
        "Reserved-5bit", "Trigger Touch", ""
        "Reserved-6bit", "Grip Touch", ""
        "Reserved-7bit", "", ""


.. only:: gun_elec_mp5

    .. csv-table:: 电动MP5按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "弹夹装入", ""
        "Hand-Left(Bit1)", "弹夹释放", ""
        "W-KEY(Bit2)", "上膛","固件处理，手动释放枪栓信号"
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "调速0(单发)", ""
        "Reserved-5bit", "调速1(连发)", ""
        "Reserved-6bit", "枪栓状态", "为1时表示枪栓拉起，不检测是否卡进槽"
        "Reserved-7bit", "", ""

    .. note:: **换弹过程**
        
        方法1 拉动枪栓，并卡进槽，取出弹夹，安装新弹夹，将枪栓拍回原位。在将枪栓拍回原位时，发送上膛信号。

        方法2 取出弹夹，安装新弹夹，拉动枪栓并释放。在枪栓释放时，发送上膛信号。


    .. note:: **最后一发子弹问题** 
        
        MP5在没有打完子弹的情况下，直接取出弹夹（非标准操作），此时会剩下一发子弹

    .. note:: **浪费弹药**
        
        MP5在没有打完子弹的情况下，拉动枪栓，会排出一发子弹，取出弹夹，安装新弹夹后，将枪栓拍回原位，完成新弹夹上膛动作。

    .. note:: **浪费弹药** 
        
        MP5拉动枪栓会消耗子弹，消耗的子弹不会击发。

.. only:: gun_elec_g17

    .. csv-table:: 电动MP5按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "弹夹装入", ""
        "Hand-Left(Bit1)", "弹夹释放", ""
        "W-KEY(Bit2)", "上膛","固件处理，手动释放枪栓信号"
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "", ""
        "Reserved-5bit", "", ""
        "Reserved-6bit", "子弹状态", "为1时表示枪管内有子弹"
        "Reserved-7bit", "", ""

    .. note:: *卡死问题*

        当G17机械卡死时，发送05FD解锁卡死。

.. only:: viewPlatForm

    .. csv-table:: 按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "Return", ""
        "Hand-Left(Bit1)", "Enter", ""
        "W-KEY(Bit2)", "音量+",""
        "Power(Bit3)", "音量-", ""
        "App(Bit4)", "毫米波雷达：动态检测", ""
        "Reserved-5bit", "毫米波雷达：静态检测", ""
        "Reserved-6bit", "", ""
        "Reserved-7bit", "", ""

    .. note:: 按键键值(Bit0-Bit3)由按键板提供，毫米波雷达键值(Bit4-Bit5)由Hub板提供，多个设备的按键需要或运算。


.. only:: bow2024

    .. csv-table:: 大弓箭（2024）按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "", ""
        "Hand-Left(Bit1)", "弓箭拉导最末端", ""
        "W-KEY(Bit2)", "弓箭拉起",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "APP按键", ""
        "Reserved-5bit", "", ""
        "Reserved-6bit", "", ""
        "Reserved-7bit", "传感器原始数值", ""

    .. note:: 扳机为模拟值，表示射击力度，该力度通过释放时的速度估计

    .. note:: 弓箭拉起后释放，不代表射击，只有扳机有数值才表示射击，否则为松开弓箭

    .. note:: 当检测状态不正确时，松开弓箭，并按APP按钮复位状态机

.. only:: bow2025

    .. csv-table:: 大弓箭（2025）按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "", ""
        "Hand-Left(Bit1)", "", ""
        "W-KEY(Bit2)", "A按键",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "B按键", ""
        "Reserved-5bit", "弓箭拉起", ""
        "Reserved-6bit", "射击指示", ""
        "Reserved-7bit", "", ""

    .. note:: 扳机为模拟值，表示弓箭位置，0为没有拉弓度，数值越大则行程越大

.. only:: sirenX_watch_elec
    
    .. csv-table:: 动捕电击手环按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "触摸按键(右)", ""
        "Hand-Left(Bit1)", "触摸按键（左）", ""
        "W-KEY(Bit2)", "触摸按键（中）",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "霍尔状态", ""
        "Reserved-5bit", "手套状态", ""
        "Reserved-6bit", "触摸状态bit0", "00-无触摸 01-单击 10-双击 11-滑动"
        "Reserved-7bit", "触摸状态bit1", ""

    .. note:: 扳机为电击的有效反馈，为0则没有电击反馈，为255则有电击反馈

    .. note:: 连续按五下触发电击，对应指令"0701010900000008"

.. only:: sirenX_watch
    
    .. csv-table:: 动捕手环按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Hand-Right(Bit0)", "触摸按键(右)", ""
        "Hand-Left(Bit1)", "触摸按键（左）", ""
        "W-KEY(Bit2)", "触摸按键（中）",""
        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "霍尔状态", ""
        "Reserved-5bit", "手套状态", ""
        "Reserved-6bit", "触摸状态bit0", "00-无触摸 01-单击 10-双击 11-滑动"
        "Reserved-7bit", "触摸状态bit1", ""

.. only:: sirenX_hub
    
    .. csv-table:: 动捕HUB按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Power(Bit3)", "电源按键", ""


.. only:: sirenX_neck
    
    .. csv-table:: 电击项圈按键定义
        :header: "对应协议数据域", "按键定义", "备注"

        "Power(Bit3)", "电源按键", ""
        "App(Bit4)", "APP按键", ""

.. only::  con_ring

    .. note:: 定位环无按键