
.. toctree::
   :maxdepth: 2

*************
外设操作
*************

.. only:: bow2024

    .. csv-table:: 大弓箭（2024）配对操作定义
        :header: "操作", "说明", "备注"

        "配对", "按住Power和App按钮", ""
        "取消配对", "按住Power后,按5次App,之后松开Power", ""


*************
连接握手流程
*************

2.1 蓝牙广播和扫描应答信息
==========================

- 广播数据包格式
   - 服务UUID: 0xFD6F
   - 厂商自定义数据: 16字节
      - 前4字节: 设备类型标识
      - 后12字节: 设备序列号

- 扫描响应数据格式
   - 包含RSSI校准值
   - 包含固件版本信息

2.2 蓝牙链路层参数交互过程
==========================

- 连接参数请求
   - 最小连接间隔: 7.5ms
   - 最大连接间隔: 15ms
   - 从机延迟: 0
   - 监控超时: 2000ms

2.3 与SDK交互过程
=================

- 握手协议版本交换
- 加密密钥协商
- 服务发现流程

2.4 基准时间同步交互过程
=======================

- 主从时钟同步
- 时间戳校准
- 漂移补偿算法

2.5 Camera同步曝光交互过程
=========================

- 曝光同步信号广播
- 从设备响应延迟测量
- 曝光时序调整
   - [待补充连接建立流程]
