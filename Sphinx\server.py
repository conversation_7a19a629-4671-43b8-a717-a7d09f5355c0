import os
import http.server
import socketserver

def start_server(port=8000, server_dir="_dst"):
    """启动HTTP服务器"""
    os.chdir(server_dir)
    handler = http.server.SimpleHTTPRequestHandler

    with socketserver.TCPServer(("", port), handler) as httpd:
        print(f"HTTP 服务器已启动: http://localhost:{port}/")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n接收到 Ctrl+C，正在退出服务器...")
            httpd.server_close()
            print("服务器已关闭。")