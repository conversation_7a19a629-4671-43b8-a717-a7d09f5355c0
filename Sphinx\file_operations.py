import os
import shutil

def copy_html_files():
    """将_build/zh_CN下的html内容复制到_dst/zh_CN"""
    BUILD_DIR = "_build/zh_CN"
    DEST_DIR = "_dst/zh_CN"
    
    os.makedirs(DEST_DIR, exist_ok=True)
    
    for xxx in os.listdir(BUILD_DIR):
        src_html_path = os.path.join(BUILD_DIR, xxx, "html")
        dest_path = os.path.join(DEST_DIR, xxx)

        if os.path.isdir(src_html_path):  # 确保 html 目录存在
            if os.path.exists(dest_path):
                shutil.rmtree(dest_path)
            shutil.copytree(src_html_path, dest_path)
            print(f"已整理: {xxx}")