.. toctree::
   :maxdepth: 2


*************
数据通道数据
*************

Characteristic 01消息
=====================

Charateristic01通道用于Controller主动上报实时交互数据（IMU、状态及按键等信息）。通过链路层帧的第一个字节数最高位进行判断区分旧版协议和最新协议。每次蓝牙连接成功，默认采用单包传输消息传输，传输0x01类型数据。


单包(旧)协议消息格式
--------------------

采用链路层单帧传输方式，数据格式如下：

.. csv-table:: 单帧数据定义
    :header: "Func", "Type", "ID", "Data"

    "Length(bit)", "8", "8", "18byte"
    "Value", "数据类型", "光点/Mark ID", "IMU和按键数据"

.. csv-table:: 数据类型
    :header: "数据类型", "值"

    "欧拉角", "0x01"
    "四元素", "0x02"
    "裸数据(用于生产功能检测)", "0x03"
    "欧拉角+加速度、陀螺仪裸数据", "0x20"

.. only:: hasPhotosphere 

    .. csv-table:: 光球ID
        :header: "光球颜色", "ID"

        "红色(red)", "0"
        "绿色(green)", "1"
        "蓝绿色(blueGreen)", "2"
        "蓝色(blue)", "3"
        "紫色(purple)", "4"
        "粉红色(pink)", "5"
        "白色(white)", "30"


.. only:: not hasPhotosphere 

    * MarkID根据Mark标定算法识别出具体ID后写入。

欧拉角(0x01)数据类型
>>>>>>>>>>>>>>>>>>>>>>>>

+-------+------+----------+-----------------------+-------+------+------+------------------------+
| 名称  | 类型 | ID       | 欧拉角                | 次数  | 扳机 | 按键 | 触摸板/摇杆            |
+-------+------+----------+-------+-------+-------+-------+------+------+--------+-------+-------+
| 长度  | 8    | 8        | 24    | 24    | 24    | 24    | 8    | 8    | 2      | 14    | 16    |
| (bit) |      |          |       |       |       |       |      |      |        |       |       |
+-------+------+----------+-------+-------+-------+-------+------+------+--------+-------+-------+
| Value | 0x01 | reserved | Yaw   | Pitch | Roll  | (MSB) |      |      | events | X     | Y     |
|       |      |          | (MSB) | (MSB) | (MSB) |       |      |      |        | (MSB) | (MSB) |
+-------+------+----------+-------+-------+-------+-------+------+------+--------+-------+-------+

.. * 欧拉角数据域： 
.. 实际的欧拉角数据(yaw/pitch/roll)为32位浮点型，为了便于传输我们把实际的数据乘以10000，放在数据帧对应的yaw轴、pitch轴、roll轴域，数据的最高位为符号位。 
.. 例如：1193046表示119.3016°，-74566表示-7.4566° 

.. * 次数： 
.. 每成功发送一次数据次数值加1，断开连接会清零。 

.. * 扳机： 
.. 数字开关模拟，扳机项取值为0或者255，0表示松开，255表示按下。模拟扳机将采集的行程值
.. 转换到0-255范围，0代表起始行程，255最大行程。 

* 按键数据域：

+-----+-----+-----+-----+-----+------------+-----+---------+----------+
| Bit |  7  |  6  |  5  |  4  |  3         |  2  |  1      |  0       |
+-----+-----+-----+-----+-----+------------+-----+---------+----------+
| Key |    Reserved     | App | Power/Home |W-KEY|Hand-Left|Hand-Right|
+-----+-----+-----+-----+-----+------------+-----+---------+----------+

四元数(0x02)数据类型
>>>>>>>>>>>>>>>>>>>>>>>>

+-------+------+----------+-----------------------+-------+------+------+------------------------+
| 名称  | 类型 | ID       | 四元数                        | 扳机 | 按键 | 触摸板/摇杆            |
+-------+------+----------+-------+-------+-------+-------+------+------+--------+-------+-------+
| 长度  | 8    | 8        | 24    | 24    | 24    | 24    | 8    | 8    | 2      | 14    | 16    |
| (bit) |      |          |       |       |       |       |      |      |        |       |       |
+-------+------+----------+-------+-------+-------+-------+------+------+--------+-------+-------+
| Value | 0x02 | reserved | Q1-w  | Q2-x  | Q3-y  | Q4-z  |      |      | events | X     | Y     |
|       |      |          | (MSB) | (MSB) | (MSB) | (MSB) |      |      |        | (MSB) | (MSB) |
+-------+------+----------+-------+-------+-------+-------+------+------+--------+-------+-------+

.. note:: 
    
    - 四元数w的实际值=w/0x7FFFFF,例如：
    - Dec(1193046)-> Float(1193046.0/0x7FFFFF)-> 0.14222218301560677 
    - 数据帧中的其它域格式参考类型0x01相对应的数据域

Sensor&Button(0x03)数据类型
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

Button裸数据(0x04)数据类型
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

.. only:: hasBulletCountGun

    弹夹ID(0x05)数据类型
    >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

    .. csv-table:: 弹夹ID(0x05)数据类型
        :header: "名称", "类型"

        "红色(red)", "0"
        "绿色(green)", "1"
        "蓝绿色(blueGreen)", "2"
        "蓝色(blue)", "3"
        "紫色(purple)", "4"
        "粉红色(pink)", "5"
        "白色(white)", "30"

    * 当切换弹夹时候会自动发送0x05数据类型，夹杂在设置的数据类型中每500ms发送一次
    * 弹夹ID域：标志弹夹的ID，用来校准新弹夹子弹数量的标志,最大支持16bit。
    * 弹夹电量域：弹夹内部电池剩余电量百分比。


欧拉角&裸数据(0x20)数据类型
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

.. only:: not hasBulletCountGun 

    +-------+------+----------+-----------------------+-----------------------+-----------------------+------+------+---------------+
    | 名称  | 类型 | ID       | 欧拉角                | 加速度                | 陀螺仪                | 扳机 | 按键 |     触摸板    |
    +-------+------+----------+-------+-------+-------+-------+-------+-------+-------+-------+-------+------+------+-------+-------+
    | 长度  | 8    | 8        | 12    | 12    | 12    | 12    | 12    | 12    | 12    | 12    | 12    | 8    | 8    | 12    | 12    |
    | (bit) |      |          |       |       |       |       |       |       |       |       |       |      |      |       |       |
    +-------+------+----------+-------+-------+-------+-------+-------+-------+-------+-------+-------+------+------+-------+-------+
    | Value | 0x02 | reserved | Yaw   | Pitch | Roll  | x     | y     | z     | x     | y     | z     |      |      | X     | Y     |
    +-------+------+----------+-------+-------+-------+-------+-------+-------+-------+-------+-------+------+------+-------+-------+

.. only:: hasBulletCountGun 

    +-------+------+----------+-----------------------+-----------------------+-----------------------+------+------+----------+--------+
    | 名称  | 类型 | ID       | 欧拉角                | 加速度                | 陀螺仪                | 扳机 | 按键 |剩余子弹数|剩余寿命|
    +-------+------+----------+-------+-------+-------+-------+-------+-------+-------+-------+-------+------+------+----------+--------+
    | 长度  | 8    | 8        | 12    | 12    | 12    | 12    | 12    | 12    | 12    | 12    | 12    | 8    | 8    |  8       | 16     |
    | (bit) |      |          |       |       |       |       |       |       |       |       |       |      |      |          |        |
    +-------+------+----------+-------+-------+-------+-------+-------+-------+-------+-------+-------+------+------+----------+--------+
    | Value | 0x02 | reserved | Yaw   | Pitch | Roll  | x     | y     | z     | x     | y     | z     |      |      | 0-100    | 0-20000|
    +-------+------+----------+-------+-------+-------+-------+-------+-------+-------+-------+-------+------+------+----------+--------+


.. include:: keyMapping.rst



Characteristic 02消息
=====================

单包(旧)协议消息格式
--------------------

设置数据通道上报类型
>>>>>>>>>>>>>>>>>>>>>>>>>>

+--------+-------+---------------------------+
|  Pos   |   0   |   1                       |
+--------+-------+---------------------------+
| Value  |  0x00 |  数据类型：               |
|        |       |    类型CODE对应数据类型表 |
+--------+-------+---------------------------+


.. only:: hasMotor 

    马达控制
    >>>>>>>>>>>>>>>>>>>>>>>>>

    .. csv-table:: 马达控制
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2", "3-4", "5-8"

        "Value", "0x01", "0x10-马达关闭   0x11-马达开启", "震动强度", "脉冲宽度(us)  MSB", "自动超时(ms)  MSB"

    * 震动强度：震动强度使用1-100控制,设置为0将使用默认震动强度
    * 脉冲宽度(微秒，pwm周期)：马达控制的PWM信号脉冲宽度,单位为us, 软件控制区间为50-50000(微秒),设置为0将使用默认震动强度（线性马达时，此位不考虑）
    * 自动超时（毫秒，线马达时，此位可用于设置是长震，还是短震）：马达控制自动超时关闭时间, 软件控制区间为50-180000(毫秒),设置为0将一直保持震动, 等待手动关闭

.. only:: hasLinearMotor

    线性马达控制
    >>>>>>>>>>>>>>>>>>>>>>>>>

    .. csv-table:: 线性马达控制
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2", "3", "4", "5-8"

        "Value", "0x01", "0x20-使用预设效果震动   0x22-使用自定义效果震动", "效果编号", "震动强度", "重复次数", "自动超时(ms)  MSB"

    .. csv-table:: 线性马达自定义效果写入
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2", "3", "4-5", "N"

        "Value", "0x01", "0x23-写入自定义震动效果", "效果编号", "写入长度(Nbyte)  MSB", "写入OFFSET", "写入数据"

.. only:: hasCustomUart

    Uart透传下发指令
    >>>>>>>>>>>>>>>>>>>>

    .. csv-table:: Uart透传下发指令
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2", "3-19"

        "Value", "0x01", "0xFF", "透传数据长度", "透传数据"

复位及进入Boot命令
>>>>>>>>>>>>>>>>>>>

.. csv-table:: 复位及进入Boot命令
    :header: "Pos(Byte)", "0(type)", "1(cmd)"

    "Value", "0x02", "0x20-系统复位     0x80-进入升级模式"

生产校准服务启动命令
>>>>>>>>>>>>>>>>>>>>>>>

.. csv-table:: 生产校准服务启动命令
    :header: "Pos(Byte)", "0(type)", "1(cmd)"

    "Value", "0x02", "0x10 使能开启所有服务，断开重连后才能看到新的服务"

配置PCBA SN
>>>>>>>>>>>>>>>>>>>

.. note:: TODO

配置设备名称
>>>>>>>>>>>>>>>>>>>

.. note:: TODO

配置model name(成品SN)
>>>>>>>>>>>>>>>>>>>>>>>

.. note:: TODO

.. only:: general_module

    设置供电类型
    >>>>>>>>>>>>>>>>>

    +--------+-------+-------+------------+
    |  Pos   |   0   |   1   |      2     |
    +--------+-------+-------+------------+
    | Value  |  0x03 |  0x40 | 供电类型： |
    |        |       |       | 0: 干电池  |
    |        |       |       | 1: 锂电池  |
    |        |       |       | 2. 长供电  |
    +--------+-------+-------+------------+

    .. note:: 该指令外设重启后生效


    设置供电类型
    >>>>>>>>>>>>>>>>>

    +--------+-------+-------+------------+
    |  Pos   |   0   |   1   |      2     |
    +--------+-------+-------+------------+
    | Value  |  0x03 |  0x41 |  5-30min   |
    +--------+-------+-------+------------+

    设置关机按键响应时间
    >>>>>>>>>>>>>>>>>>>>>>

    +--------+-------+-------+------------+
    |  Pos   |   0   |   1   |      2     |
    +--------+-------+-------+------------+
    | Value  |  0x03 |  0x42 |  5-60s     |
    +--------+-------+-------+------------+

    .. note:: 仅设置关机响应时长，开机响应时长固定3s


设置类型标识
>>>>>>>>>>>>>>>>>>>

.. csv-table:: 设置类型标识
    :header: "Pos(Byte)", "0(type)", "1(cmd)", "2-3"

    "Value", "0x03", "0xAA", "类型识别CODE 2byte (MSB)"

光点/Mark ID配置控制
>>>>>>>>>>>>>>>>>>>>>

.. csv-table:: 光点/Mark ID配置控制
    :header: "Pos(Byte)", "0(type)", "1(cmd)", "2"

    "Value", "0x04", "(0x10+id)-不修改数据通道上报数据ID序号      (0x80+id)-变更数据通道上报数据ID序号", "Mode    0x00-配置不保存flash    0x01-配置保存flash"


.. note:: 0x10+id 设置ID为0：发送命令将变更BLOB灯配置ID, 使用该命令不会修改重启之后的默认BLOB ID,并且不会变更数据流中的ID

.. note:: 0x80+id 发送命令将变更BLOB灯配置ID,使用该命令会同时变更数据流中的BLOB ID。


.. only:: hasBulletCountGun

    设置子弹数量
    >>>>>>>>>>>>>>>>>>>>>>

    .. csv-table:: 设置子弹数量
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2"

        "Value", "0x05", "(0x01)-设置子弹数", "子弹数量"
        "Value", "0x05", "(0x02)-预制子弹数（保存到flash）", "子弹数量"
        "Value", "0x05", "(0xFE)-设置调试子弹数", "子弹数量",
        "Value", "0x05", "(0xFF)-直接设置子弹（无需上膛）", "子弹数量"

    .. note::  TODO

    卡弹解锁
    >>>>>>>>>>>>>>>>>>>>>    

    +--------+-------+-------+
    |  Pos   |   0   |   1   |
    +--------+-------+-------+
    | Value  |  0x05 |  0xFD |
    +--------+-------+-------+

    设置枪使用寿命
    >>>>>>>>>>>>>>>>>>>>>    

    +--------+-------+-------+------------+
    |  Pos   |   0   |   1   |    2-3     |
    +--------+-------+-------+------------+
    | Value  |  0x05 |  0x03 | 使用寿命   |
    +--------+-------+-------+------------+

    .. note::  TODO

.. only:: gun_airBurst

    设置扳机触发阈值（空爆枪）
    >>>>>>>>>>>>>>>>>>>>>>>>>>>>>

    +--------+-------+-------+------------+
    |  Pos   |   0   |   1   |      2     |
    +--------+-------+-------+------------+
    | Value  |  0x05 |  0x0F | 阈值：1-20 |
    +--------+-------+-------+------------+

    .. note:: 阈值单位为重力加速度g


设置时间同步MAC地址（仅有线）
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

+--------+-------+-------+------------+
|  Pos   |   0   |   1   |    2-7     |
+--------+-------+-------+------------+
| Value  |  0x06 |  0x01 | MAC地址    |
+--------+-------+-------+------------+

.. note:: 该指令目前只在Modbus有线模式存在，通过Modbus主机下发，SDK不关心

.. only:: hasElec

    电击控制指令
    >>>>>>>>>>>>>>>>>>>>>>

    .. csv-table:: 电击项圈-电击控制指令
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2", "3", "4-7"

        "Value", "0x07", "0x01", "--部位--      bit0-颈部左前方      bit1-颈部左后方      bit2-颈部右前方      bit3-颈部右后方","强度（0-100）", "持续时间(ms) 0xFFFF为连续模式"

    .. csv-table:: 电击手环-电击控制指令
        :header: "Pos(Byte)", "0(type)", "1(cmd)", "2", "3", "4-7"

        "Value", "0x07", "0x02", "部位      bit0-手部","强度（0-100）", "持续时间(ms) 0xFFFF为连续模式"
