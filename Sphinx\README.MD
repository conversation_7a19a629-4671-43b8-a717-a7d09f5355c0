# 项目编译指南

## 环境准备

1. 确保已安装Python 3.8或更高版本
2. 推荐使用虚拟环境：

```bash
python -m venv venv
```

## 安装依赖

激活虚拟环境后安装依赖：

```bash
# Windows
venv\Scripts\activate.bat
pip install -r requests.txt
```

## 构建项目

使用提供的构建脚本：

```bash
build_all.bat
```

或手动执行：

```bash
python build.py
python file_operations.py
```

## 常见问题

### sphinxcontrib.seqdiag导入错误

确保虚拟环境激活且PYTHONPATH设置正确：

```bash
set PYTHONPATH=%PYTHONPATH%;%CD%
```

## 构建输出

构建生成的HTML文档位于`_build/zh_CN/`各子目录中