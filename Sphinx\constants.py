CONTROLLER_LIST = {
    "Saber" : {"name":"<PERSON>'s Saber", "code": {0x3030: "<PERSON>'s Saber"}, "tags":["hasPhotosphere"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "Kylo" : {"name":"<PERSON><PERSON><PERSON> Ren's Lightsaber", "code": {0x3031: "<PERSON><PERSON><PERSON> Ren's Lightsaber"}, "tags":["hasPhotosphere"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "Pick_up" : {"name":"Mirage AR Controller","code": {0x3032: "Mirage AR Controller"}, "tags":["hasPhotosphere"], "type":"光球跟踪6Dof手柄", "pair":True, "info":"Mirage 2B手柄"},
    "Universal" : {"name":"Universal Controller","code": {0x3033: "Universal Controller"}, "tags":["hasPhotosphere"], "type":"光球跟踪6Dof手柄", "pair":True, "info":"Mirage 1.5手柄"},
    "X3C01" : {"name":"3DOF手柄","code": {0x3230: "X3C01-XXXX"}, "tags":[], "type":"3Dof手柄", "pair":True, "info":"XXXX 是蓝牙Mac后四位, 兼容外设也被放在这里(伪装成X3C01)"},
    "AIO" : {"name":"AIO DVT1手柄","code": {0x3130:"AIO-CON-L", 0x3131:"AIO-CON-R"}, "tags":[], "type":"Mark跟踪6Dof手柄", "pair":True, "info":"Mark+3Dof手柄或者Mark+Mirage 2B 手柄；"},
    "AIO_CON" : {"name":"被动环形手柄","code": {0x313A:"AIO-CON-L", 0x313B:"AIO-CON-R"}, "tags":[], "type":"Mark跟踪6Dof手柄", "pair":True, "info":""},
    "IR_CON" : {"name":"主动环形手柄","code": {0x313B:"IR-CON-L", 0x313C:"IR-CON-R"}, "tags":[], "type":"Mark跟踪6Dof手柄", "pair":True, "info":"MarkID固定为0x5C(左) 0x5D(右)"},
    # "AIO_Cube":{}, 
    # "AIO_TouchPad":{},
    "gun_92":{"name":"92G不可动", "code": {0x3135: "AIO-ShortGun"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_95":{"name":"95-1不可动", "code": {0x3136: "AIO-LongGun"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_191":{"name":"191/192不可动", "code": {0x3137: "AIO-LongGun-191"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "Recorder":{"name":"执法记录仪", "code": {0x3140: "AIO-Recorder"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},

    "TearTrap":{"name":"催泪喷雾器", "code": {0x3141: "AIO-Tear-gas"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "StunGun":{"name":"电击枪", "code": {0x3142: "AIO-Stun-Gun"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "Batons":{"name":"警棍", "code": {0x3143: "AIO-Batons"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "FlashLight":{"name":"手电筒", "code": {0x3144: "AIO-FlashLight"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_elec_95":{"name":"95-1电动", "code": {0x3145: "AIO-Elec-95-1"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_air_95":{"name":"95-1气动", "code": {0x3146: "AIO-Air-95-1"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_elec_92":{"name":"92G电动", "code": {0x3147: "AIO-Elec-92G"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_air_92":{"name":"92G气动", "code": {0x3148: "AIO-Air-92G"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_elec_191":{"name":"191/192电动", "code": {0x3149: "AIO-Elec-191"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_air_191":{"name":"191/192气动", "code": {0x314A: "AIO-Air-191"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_snipe":{"name":"狙击", "code": {0x314B: "AIO-Snipe"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "gun_snipe_grenade":{"name":"狙击榴", "code": {0x314C: "AIO-Snipe-Grenade"}, "tags":["hasBulletCountGun"], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "sirenX_watch": {"name":"动捕手环", "code": {0x314D:"SirenX-Watch-L", 0x314E:"SirenX-Watch-R"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "sirenX_hub": {"name":"动捕hub", "code": {0x314F:"SirenX-Hub"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "sirenX_neck": {"name":"动捕电击项圈", "code": {0x3150:"SirenX-Neck"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "con_ring": {"name":"定位环", "code": {0x3153:"CON-Ring-Pro", 0x3154:"CON-Ring-L", 0x3155:"CON-Ring-R"}, "tags":[], "type":"光球跟踪6Dof手柄", "pair":True, "info":""},
    "viewPlatForm":{"name":"观景台", "code":{0x3156: "View-Platform-L/View-Platform-Hub", 0x3157:"View-Platform-C", 0x3158:"View-Platform-R", 0x3159:"View-Platform-NFC", 0x315A:"View-Platform-IMU"}, "tags":[], "type":"", "pair": False, "info":""},
    "gun_elec_mp5":{"name": "MP5电动", "code":{0x3145: "AIO-Elec-MP5"}, "tags":["hasBulletCountGun"], "type":"", "pair": True, "info":"~~0x3145~~复用95-1电动类型"},
    "gun_elec_g17":{"name": "G17电动", "code":{0x3147: "AIO-Elec-G17"}, "tags":["hasBulletCountGun"], "type":"", "pair": True, "info":"~~0x3147~~复用92-1电动类型"},
    "gun_airBurst": {"name":"空爆枪", "code":{0x3136:""}, "tags":[], "type":"demo", "pair": False, "info":""},
    "general_module":{"name":"通用模块", "code":{0x3131:""}, "tags":[], "type":"demo", "pair":True, "info":""},
    "bow2024":{"name":"大弓箭(2024)", "code":{0x315C:"AIO-Bow2024"}, "tags":[], "type":"", "pair":True, "info":""},
    "bow2025":{"name":"大弓箭(2025)", "code":{0x315F:"AIO-Bow2025"}, "tags":[], "type":"", "pair":True, "info":""},
    "sirenX_watch_elec":{"name":"动捕手环电动", "code":{0x3145:"SirenX-Watch-Elec"}, "tags":["hasElec"], "type":"", "pair":True, "info":""},
}

CONTROLLER_GROUP = {
    "sirenX": {"name":"动捕系列", "controller": ["sirenX_watch", "sirenX_hub", "sirenX_neck"]},
    "gun":{"name":"不可动枪系列", "controller": ["gun_92", "gun_95", "gun_191"]},
    "gun_elec":{"name":"电动枪系列", "controller": ["gun_elec_92", "gun_elec_95", "gun_elec_191"]},
    "gun_air":{"name":"气动枪系列", "controller": ["gun_air_92", "gun_air_95", "gun_air_191"]},
}

TARGET_NAMES = {}
for i in CONTROLLER_LIST:
    TARGET_NAMES[i] = CONTROLLER_LIST[i]["name"]

TARGETS = list(TARGET_NAMES.keys())

'''
    hasPhotosphere: 具备光球
    hasBulletCountGun: 带子弹协议的枪
    hasMotor: 具备马达
    hasLinearMotor: 具备线性马达
    hasCustomUart: 具备自定义串口透传
'''