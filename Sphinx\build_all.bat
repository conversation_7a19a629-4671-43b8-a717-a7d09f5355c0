@echo off
setlocal

echo 正在激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo 错误: 无法激活虚拟环境
    pause
    exit /b 1
)

echo 正在执行build.py...
python build.py
if %errorlevel% neq 0 (
    echo 错误: build.py执行失败
    pause
    exit /b 1
)

echo 正在执行file_operations.py...
python file_operations.py
if %errorlevel% neq 0 (
    echo 错误: file_operations.py执行失败
    pause
    exit /b 1
)

echo 所有操作已完成
pause